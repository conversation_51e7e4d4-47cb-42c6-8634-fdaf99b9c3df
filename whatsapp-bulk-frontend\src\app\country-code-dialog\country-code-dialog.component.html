<div class="container mat-dialog-container">
    <h2 mat-dialog-title>Country Code Settings</h2>
  
    <mat-dialog-content>
  
      <!-- Country Code Option Dropdown -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Country Code Option</mat-label>
        <mat-select [(ngModel)]="countryCodeOption">
          <mat-option value="without">Number without country code</mat-option>
          <mat-option value="with">Number contains country code</mat-option>
          <mat-option value="column">Get country code from Excel column</mat-option>
        </mat-select>
      </mat-form-field>
  
      <!-- Add Country Code -->
      <mat-form-field
        appearance="outline"
        class="full-width"
        *ngIf="countryCodeOption === 'without'"
      >
        <mat-label>Add Country Code</mat-label>
        <mat-select [(ngModel)]="selectedCode">
          <mat-option
            *ngFor="let code of countryCodes"
            [value]="code.value"
          >
            {{ code.label }}
          </mat-option>
        </mat-select>
      </mat-form-field>
  
      <!-- Select Column from Excel -->
      <mat-form-field
        appearance="outline"
        class="full-width"
        *ngIf="countryCodeOption === 'column'"
      >
        <mat-label>Select Country Code Column</mat-label>
        <mat-select [(ngModel)]="selectedColumn">
          <mat-option *ngFor="let col of excelColumns" [value]="col">
            {{ col }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </mat-dialog-content>
  
    <mat-dialog-actions align="end" class="dialog-buttons">
      <button mat-stroked-button (click)="closeDialog()">Cancel</button>
      <button mat-flat-button color="primary" (click)="save()">Done</button>
    </mat-dialog-actions>
  </div>
  
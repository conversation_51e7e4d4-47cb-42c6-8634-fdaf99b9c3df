<!-- <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
<div class="main-panel">
<div class="max-w-[45vw] h-[25vw] mx-auto mt-10 bg-white rounded-2xl shadow-xl p-6 overflow-auto">
    <div *ngIf="currentStep === 1">
        <h2>Select Spreadsheet</h2>
        <div class="drop-area" (drop)="onDrop($event)" (dragover)="onDragOver($event)">
    
            <i class="fa-solid fa-file-excel fa-2x"></i>
            <p>Drag an Excel Sheet file here or click the button below to upload.</p>
    
            <input type="file" #fileInput (change)="onFileSelected($event)" hidden />
            <button type="button" class="upload-button" (click)="fileInput.click()">Select File</button>
    
            <p class="info-text">Maximum file size: 5 MB</p>
        </div>
        </div>

    <div *ngIf="currentStep === 2">
        <h2 class="text-xl font-semibold mb-4">Select Spreadsheet</h2>
        <div class="flex items-center justify-between border rounded-lg p-3 mb-4">
            <img src="assets/excel-icon.png" alt="Excel Icon" class="w-6 h-6" />
            <span class="flex-1 ml-2">{{ fileName }}</span>
            <button class="text-gray-600 hover:text-red-600" (click)="removeFile()">✕</button>
        </div>
        <label class="block mb-1 font-medium">Select Sheet</label>
        <select class="border p-2 w-full rounded-md" [(ngModel)]="selectedSheet">
            <option *ngFor="let sheet of sheetNames" [value]="sheet">{{ sheet }}</option>
        </select>
        <p class="text-blue-600 mt-2">{{ sheetNames.length }} Sheets found</p>
    </div>

    <div *ngIf="currentStep === 3">
        <h2 class="text-xl font-semibold mb-4">Select Spreadsheet</h2>
        <div class="flex items-center justify-between border rounded-lg p-3 mb-4">
            <img src="assets/excel-icon.png" alt="Excel Icon" class="w-6 h-6" />
            <span class="flex-1 ml-2">{{ fileName }}</span>
            <button class="text-gray-600 hover:text-red-600" (click)="removeFile()">✕</button>
        </div>
        <label class="block mb-1 font-medium">Select Sheet</label>
        <select class="border p-2 w-full rounded-md mb-4" [(ngModel)]="selectedSheet">
            <option *ngFor="let sheet of sheetNames" [value]="sheet">{{ sheet }}</option>
        </select>

        <label class="block font-medium mb-1">Select Rows</label>
        <div class="flex items-center space-x-4 mb-4">
            <label class="flex items-center space-x-2">
                <input type="radio" name="rowOption" [(ngModel)]="sendAll" [value]="true" />
                <span>Send to All</span>
            </label>
            <label class="flex items-center space-x-2">
                <input type="radio" name="rowOption" [(ngModel)]="sendAll" [value]="false" />
                <span>Send to Selected</span>
            </label>
        </div>

        <div *ngIf="!sendAll" class="flex space-x-4 mb-2">
            <input type="number" class="border p-2 rounded-md w-1/2" placeholder="Start row number"
                [(ngModel)]="startRow" />
            <input type="number" class="border p-2 rounded-md w-1/2" placeholder="End row number"
                [(ngModel)]="endRow" />
        </div>
        <p class="text-blue-600">{{ getRecipientCount() }} Recipients selected</p>
    </div>

    <div *ngIf="currentStep === 4">
        <h2 class="text-xl font-semibold mb-4">Map Fields</h2>
        <div class="mb-4">
            <label class="block mb-1 font-medium">Country Code</label>
            <div class="flex items-center">
                <span class="text-sm mr-2">Add (+91) to number</span>
                <a href="#" class="text-blue-600 text-sm underline">CHANGE</a>
            </div>
        </div>

        <div class="mb-4">
            <label class="block mb-1 font-medium">Phone number</label>
            <select class="border p-2 w-full rounded-md" [(ngModel)]="phoneField">
                <option *ngFor="let field of fields" [value]="field">{{ field }}</option>
            </select>
        </div>

        <div class="mb-4">
            <label class="block mb-1 font-medium">Message</label>
            <select class="border p-2 w-full rounded-md" [(ngModel)]="messageField">
                <option *ngFor="let field of fields" [value]="field">{{ field }}</option>
            </select>
        </div>

        <div class="text-center text-gray-500 mb-4">— OR —</div>

        <textarea placeholder="Write Message" class="border p-2 w-full rounded-md h-24 mb-4"
            [(ngModel)]="customMessage"></textarea>

        <div class="flex items-center justify-around mt-2">
            <div class="text-center">
                <div class="bg-blue-100 rounded-full p-3">
                    <img src="assets/image-icon.png" class="w-6 h-6 mx-auto" />
                </div>
                <p class="text-sm mt-1">Image</p>
            </div>
            <div class="text-center">
                <div class="bg-blue-100 rounded-full p-3">
                    <img src="assets/video-icon.png" class="w-6 h-6 mx-auto" />
                </div>
                <p class="text-sm mt-1">Video</p>
            </div>
            <div class="text-center">
                <div class="bg-blue-100 rounded-full p-3">
                    <img src="assets/doc-icon.png" class="w-6 h-6 mx-auto" />
                </div>
                <p class="text-sm mt-1">Document</p>
            </div>
        </div>
    </div>

  
    <div class="mt-6 flex justify-between">
        <button class="border border-blue-600 text-blue-600 px-4 py-2 rounded-md" (click)="prevStep()">Back</button>
        <button class="bg-blue-600 text-white px-4 py-2 rounded-md" (click)="nextStep()">Next</button>
    </div>
</div>
</div>
   -->
<!-- Add this inside <head> -->
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

<div class="main-panel">

    <!-- View: Upload Excel File -->
    <div *ngIf="currentView === 'upload'">
        <h4 class="heading">Select Spreadsheet</h4>

        <div class="drop-area" (drop)="onDrop($event)" (dragover)="onDragOver($event)" (dragleave)="onDragLeave($event)"
            [class.drag-over]="dragging">

            <img src="assets/excel-icon.png" class="icon" />
            <p>Drag an Excel Sheet file here or click the button below to upload.</p>

            <input type="file" (change)="onFileSelected($event)" hidden #fileInput accept=".xlsx, .xls" />
            <button mat-raised-button color="primary" (click)="fileInput.click()">Select File</button>

            <div class="file-size-info">Maximum file size: 5 MB</div>
            <div *ngIf="fileError" class="error-text">{{ fileError }}</div>
        </div>

        <div class="action-buttons">
            <button mat-stroked-button color="accent">Back</button>
        </div>
    </div>

    <!-- View: Sheet & Row Selection -->
    <div *ngIf="currentView === 'sheet'">
        <h4 class="heading">Select Spreadsheet</h4>

        <!-- Uploaded File Box -->
        <div *ngIf="fileName" class="file-display mat-elevation-z1">
            <div class="file-info">
                <mat-icon>description</mat-icon>
                <span>{{ fileName }}</span>
            </div>
            <button mat-icon-button color="warn" (click)="clearFile()">
                <mat-icon>close</mat-icon>
            </button>
        </div>
        
        <!-- Sheet Dropdown -->
        <mat-form-field appearance="outline" class="full-width">
            <mat-label>Select Sheet</mat-label>
            <mat-select [(ngModel)]="selectedSheet" (selectionChange)="onSheetSelect()">
                <mat-option *ngFor="let sheet of sheetNames" [value]="sheet">
                    {{ sheet }}
                </mat-option>
            </mat-select>
        </mat-form-field>
        
        <!-- Row Option Title -->
        <div class="section">
            <label class="section-title">Select Rows</label>
        
            <mat-radio-group [(ngModel)]="rowOption" (change)="onRowOptionChange()" class="row-options">
                <mat-radio-button value="all" >Send to All</mat-radio-button>
                <mat-radio-button value="selected">Send to Selected</mat-radio-button>
            </mat-radio-group>
        </div>
        
        <!-- Row Range -->
        <div class="row-range" *ngIf="rowOption === 'selected'">
            <mat-form-field appearance="outline" class="half-width">
                <mat-label>Start row</mat-label>
                <input matInput type="number" [(ngModel)]="startRow" (input)="updateRecipientCount()" />
            </mat-form-field>
        
            <mat-form-field appearance="outline" class="half-width">
                <mat-label>End row</mat-label>
                <input matInput type="number" [(ngModel)]="endRow" (input)="updateRecipientCount()" />
            </mat-form-field>
        </div>
        
        <!-- Recipient Count -->
        <div *ngIf="recipientCount > 0" class="recipient-count">
            <span>{{ recipientCount }} Recipients selected</span>
        </div>
        <div class="action-buttons sticky-buttons">
            <button mat-stroked-button color="primary" (click)="currentView = 'upload'">Back</button>
            <button mat-raised-button color="primary" (click)="currentView = 'mapFields'">Next</button>
        </div>
        </div>
        <!-- View: Map Fields -->
        <div *ngIf="currentView === 'mapFields'" class="map-fields-form">
            <h4 class="heading">Map Fields</h4>
        
            <div class="field-value">
                Add
                <ng-container *ngIf="countryCodeOption === 'without'">
                    {{ getSelectedCountryCodeOnly() }}
                </ng-container>
                <ng-container *ngIf="countryCodeOption === 'with'">
                    (Included in number)
                </ng-container>
                <ng-container *ngIf="countryCodeOption === 'column'">
                    (From "{{ selectedCountryCodeColumn }}")
                </ng-container>
                to number
                <a class="change-link" (click)="openCountryCodeDialog()">CHANGE</a>
            </div>
              
        
            <!-- Phone Number Dropdown with Remove Icon -->
            <div class="message-input-wrapper">
                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Phone number</mat-label>
                    <mat-select [(ngModel)]="selectedPhoneField">
                        <mat-option *ngFor="let column of excelColumns" [value]="column">{{ column }}</mat-option>
                    </mat-select>
                </mat-form-field>
            
                <!-- Remove Icon aligned beside the dropdown -->
                <button mat-icon-button class="remove-btn" *ngIf="selectedPhoneField" (click)="clearPhoneSelection()">
                    <mat-icon style="color: red;">close</mat-icon>
                </button>
            </div>               
        
        <!-- Excel Dropdown -->
        <mat-form-field appearance="outline" class="full-width">
            <mat-label>Message</mat-label>
            <mat-select [(ngModel)]="selectedMessageField" (selectionChange)="onMessageFieldChange()"
                [disabled]="!!customMessage">
                <mat-option *ngFor="let column of excelColumns" [value]="column">{{ column }}</mat-option>
            </mat-select>
            <!-- Remove Icon -->
            <button mat-icon-button class="remove-btn" *ngIf="selectedMessageField || customMessage"
                (click)="clearMessageSelection()">
                <mat-icon style="color: red;">close</mat-icon>
            </button>
        </mat-form-field>
        
        <div class="or-separator">OR</div>
        
        <!-- Message + Remove Button -->
        <div class="message-input-wrapper">
            <textarea matInput placeholder="Write or choose template" class="write-message" (click)="goToTemplateMessage()"
                [disabled]="!!selectedMessageField" [value]="customMessage || ''" readonly style="cursor: pointer;"></textarea>
        </div>           
        <div class="form-group">
            <label class="checkbox-group">
                <input type="checkbox" [(ngModel)]="showAttachments" />
                Add Attachments
            </label>
        
            <div *ngIf="showAttachments && selectedFiles.length === 0" class="attachment-options">
                <!-- IMAGE -->
                <div class="option" (click)="triggerFileSelect('image')">
                    <div class="circle blue">
                        <span class="material-icons">photo_camera</span>
                    </div>
                    <div class="label">Image</div>
                    <input type="file" accept="image/*" #imageInput hidden (change)="handleMediaFile($event, 'Image')" />
                </div>
        
                <!-- VIDEO -->
                <div class="option" (click)="triggerFileSelect('video')">
                    <div class="circle blue">
                        <span class="material-icons">videocam</span>
                    </div>
                    <div class="label">Video</div>
                    <input type="file" accept="video/*" #videoInput hidden (change)="handleMediaFile($event, 'Video')" />
                </div>
        
                <!-- DOCUMENT -->
                <div class="option" (click)="triggerFileSelect('document')">
                    <div class="circle blue">
                        <span class="material-icons">insert_drive_file</span>
                    </div>
                    <div class="label">Document</div>
                    <input type="file" accept=".pdf,.doc,.docx,.xls,.xlsx,.txt" #docInput hidden
                        (change)="handleMediaFile($event, 'Document')" />
                </div>
        
                <!-- DISABLED OPTION -->
                <div class="option disabled">
                    <div class="circle gray">
                        <span class="material-icons">poll</span>
                    </div>
                    <div class="label gray">Poll</div>
                </div>
            </div>
        
            <div class="file-preview" *ngFor="let fileItem of selectedFiles; let i = index">
                <div class="preview-left">
                    <div class="preview-left-top">
                        <span class="material-icons file-icon">
                            {{
                            fileItem.type === 'image'
                            ? 'photo_camera'
                            : fileItem.type === 'video'
                            ? 'videocam'
                            : 'insert_drive_file'
                            }}
                        </span>
                        <span class="file-name">{{ fileItem.file.name }}</span>
                        <span class="file-size">• {{ (fileItem.file.size / 1024 / 1024).toFixed(2) }} MB</span>
                    </div>
                    <span class="error" *ngIf="fileItem.error">{{ fileItem.error }}</span>
                </div>
        
                <div class="preview-right">
                    <span class="remove-btn" (click)="removeFile(i)">&#x2716;</span>
                </div>
            </div>
            </div>        
            <div class="action-buttons sticky-buttons">
                <button mat-stroked-button color="primary" (click)="currentView = 'sheet'">Back</button>
                <button mat-raised-button color="primary" (click)='goToPreview()'>Next</button>
            </div>
        </div>
        <div *ngIf="currentView === 'preview'" class="preview-container">
            <h3 class="preview-title">WhatsApp Message Preview</h3>
        
            <div class="template-name-line">
                Template Name:- <strong>{{ getTemplateName() }}</strong>
            </div>
        
            <div class="whatsapp-preview-card">
                <div class="whatsapp-header">
                    <span class="whatsapp-to-label">To: {{ getSelectedPhoneNumber() }}</span>
                    <span class="whatsapp-counter">{{ currentIndex + 1 }}/{{ excelData.length }}</span>
                </div>
        
                <div class="whatsapp-message-bubble">
                    <div class="message-content">
                        {{ getSelectedMessage() }}
                        <br /><br />
                        <strong>Powered by cigtwa.com</strong>
                    </div>
                </div>
                <div class="navigation-buttons">
                    <button mat-icon-button (click)="prev()" [disabled]="currentIndex === 0">
                        <mat-icon>arrow_back</mat-icon>
                    </button>
                
                    <button mat-icon-button (click)="next()" [disabled]="currentIndex >= excelData.length - 1">
                        <mat-icon>arrow_forward</mat-icon>
                    </button>  </div>
            </div>
        
            <div class="preview-buttons">
                <button mat-stroked-button color="primary" (click)="currentView = 'mapFields'">Back</button>
                <button mat-raised-button color="primary" (click)="sendMessagesFromExcel()">Send Messages</button>
            </div>
        </div>                    
</div>

